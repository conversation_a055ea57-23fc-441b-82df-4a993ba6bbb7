@extends('layouts.frontend')

@section('title', $category->name . ' - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics'))
@section('description', $category->description ?? 'Browse our ' . $category->name . ' products with professional logistics support.')

@section('content')

<!-- Hero Section -->
<x-page-hero 
    title="{{ $category->name }}"
    subtitle="{{ $category->description ?? 'Quality products with professional logistics support' }}"
    description="Browse our comprehensive range of {{ strtolower($category->name) }} with professional logistics support."
    :breadcrumbs="[
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Products', 'url' => route('products.index')],
        ['title' => $category->name]
    ]"
    gradient="from-blue-900 via-green-800 to-gray-900"
/>

<!-- Search Section -->
<section class="py-12 bg-white border-b border-gray-200">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto">
            <form method="GET" class="flex gap-4">
                <input type="text" name="search" 
                       class="flex-1 px-6 py-4 rounded-xl border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" 
                       placeholder="Search in {{ $category->name }}..." 
                       value="{{ request('search') }}">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-xl font-semibold transition-colors shadow-lg hover:shadow-xl">
                    <i class="fas fa-search mr-2"></i>Search
                </button>
            </form>
        </div>
    </div>
</section>

<!-- Products Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Filters -->
            <div class="lg:w-1/4">
                <div class="bg-white rounded-2xl shadow-lg p-6 sticky top-8">
                    <h3 class="text-lg font-bold text-gray-900 mb-6">
                        <i class="fas fa-filter mr-2 text-green-600"></i>Filters
                    </h3>
                    
                    <form method="GET" class="space-y-6">
                        <input type="hidden" name="search" value="{{ request('search') }}">
                        
                        <!-- Subcategories -->
                        @if($subcategories->count() > 0)
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Subcategories</h4>
                            <div class="space-y-2">
                                @foreach($subcategories as $subcategory)
                                <label class="flex items-center">
                                    <input type="checkbox" name="subcategory[]" value="{{ $subcategory->slug }}" 
                                           {{ in_array($subcategory->slug, request('subcategory', [])) ? 'checked' : '' }}
                                           class="text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <span class="ml-2 text-gray-700">{{ $subcategory->name }}</span>
                                </label>
                                @endforeach
                            </div>
                        </div>
                        @endif
                        
                        <!-- Price Range -->
                        @if($priceRange && $priceRange->min_price && $priceRange->max_price)
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Price Range</h4>
                            <div class="grid grid-cols-2 gap-2">
                                <input type="number" name="min_price" 
                                       class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500" 
                                       placeholder="Min" value="{{ request('min_price') }}" 
                                       min="{{ $priceRange->min_price }}" max="{{ $priceRange->max_price }}">
                                <input type="number" name="max_price" 
                                       class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500" 
                                       placeholder="Max" value="{{ request('max_price') }}" 
                                       min="{{ $priceRange->min_price }}" max="{{ $priceRange->max_price }}">
                            </div>
                            <p class="text-sm text-gray-500 mt-2">
                                Range: @currency($priceRange->min_price) - @currency($priceRange->max_price)
                            </p>
                        </div>
                        @endif
                        
                        <div class="space-y-3">
                            <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors">
                                Apply Filters
                            </button>
                            <a href="{{ route('categories.show', $category) }}" class="block w-full bg-gray-100 hover:bg-gray-200 text-gray-800 py-3 rounded-lg font-semibold transition-colors text-center">
                                Clear All
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="lg:w-3/4">
                <!-- Sort Options -->
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">{{ $category->name }}</h2>
                        <p class="text-gray-600">{{ $products->total() }} products found</p>
                    </div>
                    <div class="flex items-center gap-4">
                        <form method="GET" class="flex items-center gap-2">
                            @foreach(request()->except('sort') as $key => $value)
                                <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                            @endforeach
                            <label class="text-sm text-gray-600">Sort by:</label>
                            <select name="sort" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500" onchange="this.form.submit()">
                                <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest First</option>
                                <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                                <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Name A-Z</option>
                                <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                                <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                            </select>
                        </form>
                    </div>
                </div>

                <!-- Products -->
                @if($products->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                    @foreach($products as $product)
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 card-hover animate-on-scroll overflow-hidden">
                        <div class="relative">
                            <img src="{{ $product->featured_image_url ?? 'https://via.placeholder.com/400x300?text=No+Image' }}" 
                                 alt="{{ $product->name }}" class="w-full h-64 object-cover">
                            
                            @if($product->isOnSale())
                            <span class="absolute top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                {{ $product->discount_percentage }}% OFF
                            </span>
                            @endif
                            
                            <div class="absolute top-3 right-3 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full">
                                @if($product->isOnSale())
                                    <span class="line-through text-sm">@currency($product->price)</span>
                                    <span class="font-bold ml-1">@currency($product->sale_price)</span>
                                @else
                                    <span class="font-bold">@currency($product->price)</span>
                                @endif
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <div class="mb-4">
                                @if($product->category)
                                <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mb-2">
                                    {{ $product->category->name }}
                                </span>
                                @endif
                                <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $product->name }}</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    {{ Str::limit($product->short_description, 120) }}
                                </p>
                            </div>
                            
                            <div class="flex gap-3">
                                <a href="{{ route('products.show', $product) }}" 
                                   class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-semibold transition-colors text-center">
                                    <i class="fas fa-eye mr-2"></i>View Details
                                </a>
                                <button onclick="openProductQuote('{{ $product->id }}', '{{ $product->name }}', '{{ $product->price }}')" 
                                        class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-semibold transition-colors">
                                    <i class="fas fa-quote-left mr-2"></i>Quote
                                </button>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12 flex justify-center">
                    {{ $products->appends(request()->query())->links() }}
                </div>
                @else
                <div class="text-center py-20">
                    <div class="max-w-md mx-auto">
                        <i class="fas fa-box-open text-6xl text-gray-400 mb-6"></i>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">No products found</h3>
                        <p class="text-gray-600 mb-6">No products found in {{ $category->name }}. Try adjusting your search or filters.</p>
                        <a href="{{ route('categories.show', $category) }}" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                            View All {{ $category->name }}
                        </a>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

@endsection

@include('components.quote-modal')

@push('scripts')
<script>
function openProductQuote(productId, productName, productPrice) {
    // This function is handled by the quote modal component
    if (typeof window.openProductQuote === 'function') {
        window.openProductQuote(productId, productName, productPrice);
    } else {
        // Fallback - switch to product tab and show modal
        const productTab = document.getElementById('product-tab');
        if (productTab) {
            productTab.click();
        }
        
        // Show modal
        openQuoteModal();
        
        // Add product to quote if functions are available
        if (typeof addProductToQuote === 'function') {
            addProductToQuote(productId, productName, productPrice, 1);
        }
    }
}
</script>
@endpush
