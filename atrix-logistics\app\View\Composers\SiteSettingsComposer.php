<?php

namespace App\View\Composers;

use App\Models\SiteSetting;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;

class SiteSettingsComposer
{
    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        $siteSettings = $this->getSiteSettings();
        $view->with('siteSettings', $siteSettings);
    }

    /**
     * Get site settings with caching
     */
    private function getSiteSettings(): array
    {
        return Cache::remember('global_site_settings', 3600, function () {
            try {
                return SiteSetting::pluck('value', 'key_name')->toArray();
            } catch (\Exception $e) {
                // Fallback settings if database is not available
                return [
                    'site_name' => 'Atrix Logistics',
                    'site_title' => 'Atrix Logistics - Professional Shipping Solutions',
                    'site_tagline' => 'We ship anything, anywhere, anytime',
                    'site_logo' => 'assets/images/logo.png',
                    'site_favicon' => 'assets/images/favicon.ico',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+****************',
                ];
            }
        });
    }
}
